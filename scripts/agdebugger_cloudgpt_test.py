from autogen_agentchat.teams import MagenticOneGroupChat
from autogen_agentchat.ui import <PERSON>sole
from autogen_ext.agents.web_surfer import MultimodalWebSurfer
from autogen_ext.models.openai import AzureOpenAIChatCompletionClient
from autogen_ext.models.openai.cloudgpt_aoai import get_openai_token_provider

async def get_agent_team():
    model_client = AzureOpenAIChatCompletionClient(
        model="gpt-4o-20241120",  # 使用CloudGPT支持的模型名称
    )

    surfer = MultimodalWebSurfer(
        "WebSurfer",
        model_client=model_client,
    )
    team = MagenticOneGroupChat([surfer], model_client=model_client)

    return team